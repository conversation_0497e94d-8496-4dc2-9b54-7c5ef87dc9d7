#include <Trade/Trade.mqh>

CTrade trade;

//+------------------------------------------------------------------+
//| المدخلات                                                         |
//+------------------------------------------------------------------+
input int    StartHour          = 2;      // ساعة البداية (0-23)
input int    StartMinute        = 35;     // دقيقة البداية (0-59)
input int    TimeWindowSeconds  = 10;     // نافذة زمنية لقبول الدخول (ثوان)

input double InitialLot         = 0.01;   // اللوت الابتدائي
input int    StopLossPips       = 500;    // الستوب (بيبس)
input int    TakeProfitPips     = 1000;   // الهدف (بيبس)
input double LotMultiplier      = 1.25;   // مضاعفة اللوت بعد خسارة
input int    OrderDistancePips  = 50;     // مسافة الأمر المعلق (بيبس)

input int    MaxLossTrades      = 3;      // أقصى خسائر متتالية قبل إيقاف تداول اليوم
input int    CloseHour          = 23;     // وقت إغلاق اليوم - ساعة
input int    CloseMinute        = 50;     // وقت إغلاق اليوم - دقيقة

// فلاتر الرينج والتذبذب
input ENUM_TIMEFRAMES AtrTf     = PERIOD_M15;
input int    AtrPeriod          = 14;
input int    MinATR_Pips        = 25;     // أقل ATR للتداول
input double AtrMultiplier      = 1.0;    // مضاعف ATR لمسافة الدخول الديناميكية

input ENUM_TIMEFRAMES RangeTf   = PERIOD_M5;
input int    RangeBars          = 36;     // آخر 3 ساعات تقريباً على M5
input int    MinRangePips       = 40;     // أقل نطاق للتداول

input int    MaxSpreadPips      = 3;      // حد أقصى للسبريد (بيبس)

// إدارة الانقلابات (Flips)
input int    FlipCooldownMin    = 30;     // تهدئة بعد خسارة قبل وضع العكس (دقائق)
input int    MaxFlipsPerDay     = 2;      // أقصى عدد انقلابات في اليوم

// إدارة الصفقة
input int    BreakEvenPips      = 30;     // نقل الوقف للتعادل بعد هذا الربح
input int    TrailStartPips     = 60;     // بدء التريلينغ بعد هذا الربح
input int    TrailStepPips      = 20;     // خطوة التريلينغ (المسافة خلف السعر)

input color  InfoTextColor      = clrLime;// لون النص
input int    MagicNumber        = 123456; // ماجيك نمبر

// خيارات تسهيل الاختبار/التشغيل الفوري
input bool   StartAnytime       = false;  // تجاهل نافذة الوقت (تشغيل فوري)
input bool   IgnoreFilters      = false;  // تجاهل فلاتر ATR/النطاق/السبريد
input bool   UseMarketEntry     = false;  // دخول فوري Market بدلاً من أوامر معلقة
input int    MarketDirection    = 1;      // اتجاه الدخول الفوري: 1=Buy, -1=Sell

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                 |
//+------------------------------------------------------------------+
double   pipSize, stopLossPrice, takeProfitPrice, minLot, maxLot, lotStep;
double   currentLotSize = 0.0;
double   orderDistancePrice = 0.0;

bool     tradingAllowed = true;  // السماح بالتداول
bool     closedToday    = false; // تم إغلاق اليوم
int      lossTradesCount = 0;    // خسائر متتالية
int      flipsToday      = 0;    // انقلابات اليوم
datetime lastDayChecked  = 0;
datetime lastTradeTime   = 0;    // أول وضع أوامر في اليوم
datetime lastFlipTime    = 0;    // آخر وقت خسارة/انقلاب

double   dailyProfit     = 0.0;

// كائنات العرض
string infoLabelName   = "InfoLabel";
string profitLabelName = "ProfitLabel";
string lotSizeLabelName= "LotSizeLabel";
string statusLabelName = "StatusLabel";
string lossLabelName   = "LossLabel";

//+------------------------------------------------------------------+
//| أدوات مساعدة                                                     |
//+------------------------------------------------------------------+
double NormalizePrice(double price)
{
   double tick = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   if(tick <= 0.0) tick = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   return MathRound(price / tick) * tick;
}

double NormalizeVolume(double vol)
{
   if(lotStep <= 0.0) lotStep = 0.01;
   vol = MathMax(minLot, MathMin(maxLot, vol));
   double steps = MathRound((vol - minLot)/lotStep);
   double v = minLot + steps*lotStep;
   return NormalizeDouble(v, 2);
}

bool PositionMatchesFilter()
{
   string sym = PositionGetString(POSITION_SYMBOL);
   long   mg  = (long)PositionGetInteger(POSITION_MAGIC);
   return (sym == _Symbol && mg == MagicNumber);
}

bool OrderMatchesFilter()
{
   string sym = OrderGetString(ORDER_SYMBOL);
   long   mg  = (long)OrderGetInteger(ORDER_MAGIC);
   return (sym == _Symbol && mg == MagicNumber);
}

bool HasOpenPositions()
{
   for(int i=PositionsTotal()-1; i>=0; --i)
   {
      if(PositionSelectByIndex(i) && PositionMatchesFilter())
         return true;
   }
   return false;
}

bool HasPendingOrders()
{
   for(int i=OrdersTotal()-1; i>=0; --i)
   {
      ulong ticket = OrderGetTicket(i);
      if(ticket == 0) continue;
      if(!OrderSelect(ticket)) continue;
      if(!OrderMatchesFilter()) continue;

      ENUM_ORDER_TYPE t = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
      if(t == ORDER_TYPE_BUY_STOP || t == ORDER_TYPE_SELL_STOP)
         return true;
   }
   return false;
}

void CancelAllPendingOrders()
{
   for(int i=OrdersTotal()-1; i>=0; --i)
   {
      ulong ticket = OrderGetTicket(i);
      if(ticket == 0) continue;
      if(!OrderSelect(ticket)) continue;
      if(!OrderMatchesFilter()) continue;

      ENUM_ORDER_TYPE t = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
      if(t == ORDER_TYPE_BUY_STOP || t == ORDER_TYPE_SELL_STOP)
         trade.OrderDelete(ticket);
   }
}

void CloseAllPositionsAndOrders()
{
   for(int i=PositionsTotal()-1; i>=0; --i)
   {
      if(!PositionSelectByIndex(i)) continue;
      if(!PositionMatchesFilter())  continue;
      ulong ticket = (ulong)PositionGetInteger(POSITION_TICKET);
      trade.PositionClose(ticket); // مدعوم في إصدارات MQL5 الحديثة
   }
   CancelAllPendingOrders();
}

double GetATRPips()
{
   // استخدام مؤشر ATR بطريقة متوافقة مع MQL5 (مقابض ونسخ بافر)
   int handle = iATR(_Symbol, AtrTf, AtrPeriod);
   if(handle == INVALID_HANDLE)
      return 0.0;

   double buffer[1];
   int copied = CopyBuffer(handle, 0, 0, 1, buffer);
   IndicatorRelease(handle);
   if(copied <= 0)
      return 0.0;

   double atr_points = buffer[0];
   if(atr_points <= 0.0) return 0.0;
   return atr_points / pipSize;
}

bool IsVolatilityOK()
{
   // فلتر السبريد
   if(MaxSpreadPips > 0)
   {
      long spread_points = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
      double spreadPips = (double)spread_points;
      // تحويل نقاط إلى بيبس
      if(SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 3 || SymbolInfoInteger(_Symbol, SYMBOL_DIGITS) == 5)
         spreadPips /= 10.0;
      if(spreadPips > MaxSpreadPips) return false;
   }

   // فلتر ATR
   double atrPips = GetATRPips();
   if(atrPips < MinATR_Pips) return false;

   // فلتر النطاق لآخر N شموع
   double highs[], lows[];
   if(CopyHigh(_Symbol, RangeTf, 0, RangeBars, highs) <= 0) return false;
   if(CopyLow (_Symbol, RangeTf, 0, RangeBars, lows)  <= 0) return false;

   double hh = -DBL_MAX, ll = DBL_MAX;
   int sz = MathMin(ArraySize(highs), ArraySize(lows));
   for(int i=0;i<sz;i++)
   {
      if(highs[i] > hh) hh = highs[i];
      if(lows[i]  < ll) ll = lows[i];
   }
   if(hh <= ll) return false;

   double rangePips = (hh - ll) / pipSize;
   if(rangePips < MinRangePips) return false;

   return true;
}

double DynamicOrderDistancePrice()
{
   double atrPips = GetATRPips();
   double distPips = MathMax((double)OrderDistancePips, atrPips * AtrMultiplier);
   return distPips * pipSize;
}

// دخول فوري للسوق لأغراض الاختبار/التشغيل المباشر
bool PlaceMarketOrder(int direction, double lot)
{
   MqlTick tick; if(!SymbolInfoTick(_Symbol, tick)) return false;
   lot = NormalizeVolume(lot);
   trade.SetExpertMagicNumber(MagicNumber);

   if(direction >= 1)
   {
      double sl = NormalizePrice(tick.ask - stopLossPrice);
      double tp = NormalizePrice(tick.ask + takeProfitPrice);
      if(!trade.Buy(lot, _Symbol, 0.0, sl, tp))
      { Print("Failed market BUY. Error: ", GetLastError()); return false; }
      Print("Placed MARKET BUY: lot=", lot, " SL=", sl, " TP=", tp);
      return true;
   }
   else if(direction <= -1)
   {
      double sl = NormalizePrice(tick.bid + stopLossPrice);
      double tp = NormalizePrice(tick.bid - takeProfitPrice);
      if(!trade.Sell(lot, _Symbol, 0.0, sl, tp))
      { Print("Failed market SELL. Error: ", GetLastError()); return false; }
      Print("Placed MARKET SELL: lot=", lot, " SL=", sl, " TP=", tp);
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| لوحة المعلومات                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color textColor = clrLime, int fontSize = 14)
{
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontSize);
   ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
}

void UpdateLabel(string name, string text, color textColor = clrLime)
{
   if(ObjectFind(0, name) < 0) return;
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
}

void CreateInfoPanel()
{
   CreateLabel(infoLabelName,   "Meta 5 Trading Bot", 20, 15, InfoTextColor, 16);
   CreateLabel(statusLabelName, "Status: Waiting for trading time", 20, 45, InfoTextColor);
   CreateLabel(lotSizeLabelName,"Current Lot Size: " + DoubleToString(currentLotSize, 2), 20, 75, InfoTextColor);
   CreateLabel(lossLabelName,   "Loss Trades: 0/" + IntegerToString(MaxLossTrades), 20, 105, clrRed);
   CreateLabel(profitLabelName, "Daily Profit: 0.00", 20, 135, InfoTextColor);
   CreateLabel("Copyright",     "Made by Shawn using AI software", 20, 180, clrYellow, 10);
}

void UpdateInfoPanel()
{
   string statusText = "Status: ";
   color statusColor = InfoTextColor;

   if(!tradingAllowed)
   {
      statusText += "Trading stopped (TP hit, Max Loss or EOD)";
      statusColor = clrRed;
   }
   else if(HasOpenPositions())
   {
      statusText += "Active position";
      statusColor = clrLime;
   }
   else if(HasPendingOrders())
   {
      statusText += "Pending orders placed";
      statusColor = clrYellow;
   }
   else
   {
      statusText += "Waiting for trading time";
      statusColor = clrWhite;
   }

   UpdateLabel(statusLabelName, statusText, statusColor);
   UpdateLabel(lotSizeLabelName, "Current Lot Size: " + DoubleToString(currentLotSize, 2), InfoTextColor);
   UpdateLabel(lossLabelName, "Loss Trades: " + IntegerToString(lossTradesCount) + "/" + IntegerToString(MaxLossTrades), clrRed);
   color profitColor = (dailyProfit >= 0) ? clrLime : clrRed;
   UpdateLabel(profitLabelName, "Daily Profit: " + DoubleToString(dailyProfit, 2), profitColor);
}

void DeleteInfoPanel()
{
   ObjectDelete(0, infoLabelName);
   ObjectDelete(0, statusLabelName);
   ObjectDelete(0, lotSizeLabelName);
   ObjectDelete(0, profitLabelName);
   ObjectDelete(0, lossLabelName);
   ObjectDelete(0, "Copyright");
}

//+------------------------------------------------------------------+
//| أوامر معلقة                                                      |
//+------------------------------------------------------------------+
bool PlaceBuyStop(double lot)
{
   MqlTick tick; if(!SymbolInfoTick(_Symbol, tick)) return false;
   double dist = DynamicOrderDistancePrice();
   double price = NormalizePrice(tick.ask + dist);
   double sl    = NormalizePrice(price - stopLossPrice);
   double tp    = NormalizePrice(price + takeProfitPrice);

   lot = NormalizeVolume(lot);
   trade.SetExpertMagicNumber(MagicNumber);
   if(!trade.BuyStop(lot, price, _Symbol, sl, tp))
   {
      Print("Failed to place Buy Stop. Error: ", GetLastError());
      return false;
   }
   Print("Placed Buy Stop: lot=", lot, " price=", price, " SL=", sl, " TP=", tp);
   return true;
}

bool PlaceSellStop(double lot)
{
   MqlTick tick; if(!SymbolInfoTick(_Symbol, tick)) return false;
   double dist = DynamicOrderDistancePrice();
   double price = NormalizePrice(tick.bid - dist);
   double sl    = NormalizePrice(price + stopLossPrice);
   double tp    = NormalizePrice(price - takeProfitPrice);

   lot = NormalizeVolume(lot);
   trade.SetExpertMagicNumber(MagicNumber);
   if(!trade.SellStop(lot, price, _Symbol, sl, tp))
   {
      Print("Failed to place Sell Stop. Error: ", GetLastError());
      return false;
   }
   Print("Placed Sell Stop: lot=", lot, " price=", price, " SL=", sl, " TP=", tp);
   return true;
}

// يضمن وجود أمر معلق معاكس عندما توجد صفقة ولا يوجد أوامر
bool EnsurePendingOrderExists()
{
   if(!HasOpenPositions()) return true;
   if(HasPendingOrders())  return true;

   // تهدئة بعد الخسارة
   if(FlipCooldownMin > 0 && (TimeCurrent() - lastFlipTime) < FlipCooldownMin * 60) return false;
   if(MaxFlipsPerDay > 0 && flipsToday >= MaxFlipsPerDay) return false;

   // فلتر التذبذب قبل وضع العكس
   if(!IsVolatilityOK()) return false;

   for(int i=PositionsTotal()-1; i>=0; --i)
   {
      if(!PositionSelectByIndex(i)) continue;
      if(!PositionMatchesFilter())  continue;

      ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      double posLot = PositionGetDouble(POSITION_VOLUME);
      double nextLot = NormalizeVolume(posLot * LotMultiplier);

      bool ok = (type == POSITION_TYPE_BUY) ? PlaceSellStop(nextLot) : PlaceBuyStop(nextLot);
      if(ok)
      {
         flipsToday++;
         lastFlipTime = TimeCurrent();
      }
      return ok;
   }
   return false;
}

//+------------------------------------------------------------------+
//| إدارة الصفقة (تعادل + تريلينغ)                                   |
//+------------------------------------------------------------------+
void ManageOpenPosition()
{
   for(int i=PositionsTotal()-1; i>=0; --i)
   {
      if(!PositionSelectByIndex(i)) continue;
      if(!PositionMatchesFilter())  continue;

      ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      double sl        = PositionGetDouble(POSITION_SL);
      double tp        = PositionGetDouble(POSITION_TP);

      double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      double price = (type == POSITION_TYPE_BUY ? bid : ask);

      double profitPips = (type == POSITION_TYPE_BUY ? (price - openPrice) : (openPrice - price)) / pipSize;

      bool needModify = false;
      double newSL = sl;

      // نقل الوقف للتعادل
      if(BreakEvenPips > 0 && profitPips >= BreakEvenPips)
      {
         double be = (type == POSITION_TYPE_BUY ? openPrice + 2*pipSize : openPrice - 2*pipSize); // +2 بيبس تغطية السبريد
         if(sl == 0.0 ||
            (type == POSITION_TYPE_BUY && be > sl) ||
            (type == POSITION_TYPE_SELL && be < sl))
         {
            newSL = be;
            needModify = true;
         }
      }

      // تريلينغ
      if(TrailStartPips > 0 && TrailStepPips > 0 && profitPips >= TrailStartPips)
      {
         double trailDist = TrailStepPips * pipSize;
         double trailSL = (type == POSITION_TYPE_BUY ? price - trailDist : price + trailDist);

         // لا تُنقص SL عن التعادل إذا كان مرفوعاً
         if(sl == 0.0) { /* لا شيء */ }
         if(type == POSITION_TYPE_BUY)
         {
            if(trailSL > newSL) { newSL = trailSL; needModify = true; }
         }
         else
         {
            if(trailSL < newSL) { newSL = trailSL; needModify = true; }
         }
      }

      if(needModify)
      {
         newSL = NormalizePrice(newSL);
         // تعديل بالرمز (نفترض صفقة واحدة لكل رمز)
         trade.PositionModify(_Symbol, newSL, tp);
      }
   }
}

//+------------------------------------------------------------------+
//| تتبع الربح اليومي                                                 |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
   datetime dayStart = iTime(_Symbol, PERIOD_D1, 0);
   if(!HistorySelect(dayStart, TimeCurrent()))
      return;

   double sum = 0.0;
   uint deals = HistoryDealsTotal();
   for(uint i=0; i<deals; ++i)
   {
      ulong dealTicket = HistoryDealGetTicket(i);
      if(dealTicket == 0) continue;

      string sym = HistoryDealGetString(dealTicket, DEAL_SYMBOL);
      long   mg  = (long)HistoryDealGetInteger(dealTicket, DEAL_MAGIC);
      if(sym != _Symbol || mg != MagicNumber) continue;

      double pr = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
      sum += pr;
   }
   dailyProfit = sum;
}

//+------------------------------------------------------------------+
//| OnInit                                                            |
//+------------------------------------------------------------------+
int OnInit()
{
   if(InitialLot <= 0 || StopLossPips <= 0 || TakeProfitPips <= 0 || LotMultiplier <= 1.0 || OrderDistancePips <= 0)
   {
      Print("Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }
   if(StartHour < 0 || StartHour > 23 || StartMinute < 0 || StartMinute > 59 || TimeWindowSeconds <= 0)
   {
      Print("Invalid time parameters");
      return INIT_PARAMETERS_INCORRECT;
   }

   trade.SetExpertMagicNumber(MagicNumber);

   // حساب البيب
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   pipSize = point * ((digits==3 || digits==5) ? 10.0 : 1.0);

   stopLossPrice     = StopLossPips    * pipSize;
   takeProfitPrice   = TakeProfitPips  * pipSize;
   orderDistancePrice= OrderDistancePips * pipSize;

   minLot  = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   maxLot  = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   currentLotSize = NormalizeVolume(InitialLot);

   CreateInfoPanel();
   UpdateInfoPanel();

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| OnDeinit                                                          |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   CancelAllPendingOrders();
   DeleteInfoPanel();
}

//+------------------------------------------------------------------+
//| OnTick                                                            |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime now = TimeCurrent();
   datetime dayStart = iTime(_Symbol, PERIOD_D1, 0);

   // بداية يوم جديد: إعادة تهيئة
   if(lastDayChecked < dayStart)
   {
      dailyProfit     = 0.0;
      lastTradeTime   = 0;
      lastFlipTime    = 0;
      lastDayChecked  = dayStart;
      tradingAllowed  = true;
      closedToday     = false;
      lossTradesCount = 0;
      flipsToday      = 0;
      currentLotSize  = NormalizeVolume(InitialLot);
      CancelAllPendingOrders();
      Print("New day started - Trading enabled.");
   }

   // تحديث الربح اليومي للوحة
   UpdateDailyProfit();

   // إغلاق اليوم عند الوقت المحدد
   MqlDateTime dt; TimeToStruct(now, dt);
   bool isCloseTime = (dt.hour > CloseHour) || (dt.hour == CloseHour && dt.min >= CloseMinute);
   if(isCloseTime && !closedToday)
   {
      CloseAllPositionsAndOrders();
      tradingAllowed = false;
      closedToday = true;
      Print("End of day close executed.");
   }

   // إدارة الصفقة (تعادل وتريلينغ)
   ManageOpenPosition();

   // التوقف الكلي
   if(!tradingAllowed) { UpdateInfoPanel(); return; }

   // نافذة البداية (أو تشغيل فوري عند StartAnytime)
   MqlDateTime sdt; TimeToStruct(dayStart, sdt);
   sdt.hour = StartHour; sdt.min = StartMinute; sdt.sec = 0;
   datetime startWindow = StructToTime(sdt);
   datetime endWindow   = startWindow + TimeWindowSeconds;
   bool inStartWindow   = StartAnytime || (now >= startWindow && now <= endWindow);

   // ضع الدخول الأولي مرة واحدة: Market أو Pending وفق الإعدادات
   if(inStartWindow && lastTradeTime == 0 && !HasOpenPositions() && !HasPendingOrders())
   {
      if(IgnoreFilters || IsVolatilityOK())
      {
         double lot = NormalizeVolume(currentLotSize);
         bool placed = false;
         if(UseMarketEntry)
         {
            placed = PlaceMarketOrder(MarketDirection, lot);
         }
         else
         {
            bool b1 = PlaceBuyStop(lot);
            bool b2 = PlaceSellStop(lot);
            placed = (b1 || b2);
         }
         if(placed)
         {
            lastTradeTime = now;
            Print("Initial entry placed.");
         }
      }
      else
      {
         Print("Skipped initial orders due to low volatility/range/spread filter.");
      }
   }

   // عند وجود صفقة وعدم وجود أوامر، ضَع الأمر المعاكس مع الفلاتر
   EnsurePendingOrderExists();

   UpdateInfoPanel();
}

//+------------------------------------------------------------------+
//| OnTradeTransaction                                                |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult&  result)
{
   // نهتم بالديالز (صفقات منفذة)
   if(trans.type != TRADE_TRANSACTION_DEAL_ADD)
      return;

   ulong deal_ticket = trans.deal;
   if(deal_ticket == 0) return;

   // اختيار الديل بالتيكت مباشرة (MQL5)
   if(!HistoryDealSelect(deal_ticket)) return;

   string sym = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
   long   mg  = (long)HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
   if(sym != _Symbol || mg != MagicNumber) return;

   int    entry  = (int)HistoryDealGetInteger(deal_ticket, DEAL_ENTRY);
   double profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);

   // عند إغلاق صفقة
   if(entry == DEAL_ENTRY_OUT)
   {
      UpdateDailyProfit();

      if(profit < 0.0)
      {
         lossTradesCount++;
         lastFlipTime = TimeCurrent();
         currentLotSize = NormalizeVolume(currentLotSize * LotMultiplier);

         if(lossTradesCount >= MaxLossTrades)
         {
            Print("Max consecutive losses reached. Stopping trading for today.");
            tradingAllowed = false;
            CancelAllPendingOrders();
         }
      }
      else
      {
         // ربح: إعادة اللوت وإيقاف التداول لباقي اليوم
         lossTradesCount = 0;
         currentLotSize  = NormalizeVolume(InitialLot);
         Print("Profit achieved. Stopping trading for today.");
         tradingAllowed = false;
         CancelAllPendingOrders();
      }
   }
}


